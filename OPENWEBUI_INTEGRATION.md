# Star Citizen Tools - OpenWebUI Integration Guide

This guide explains how to integrate the Star Citizen Tools with OpenWebUI and Ollama for AI-powered Star Citizen information lookup.

## 🎯 What This Provides

Your AI assistant in OpenWebUI will be able to:

- **Look up player profiles** by handle
- **Search organization information** and member lists  
- **Search Star Citizen lore** from the Galactapedia
- **Find gameplay guides** from the Star Citizen Tools wiki
- **Answer questions** about ships, planets, species, and game mechanics

## 📋 Prerequisites

1. **OpenWebUI** installed and running
2. **Ollama** with a compatible model (Qwen 2.5, Llama 3.1, etc.)
3. **Python 3.6+** with the Star Citizen Tools project
4. **Your RSI API key** configured (already done: `13e27ef0fc54d0ba93fa1bc820a79780`)

## 🚀 Installation Steps

### Step 1: Verify Your Setup

First, make sure your Star Citizen Tools are working:

```bash
# Test the configuration
python client.py config-status

# Test a lookup
python client.py call citizens profile --params handle="KenzoKai"
```

### Step 2: Copy Functions to OpenWebUI

You have several options for adding these functions to OpenWebUI:

#### Option A: Direct File Copy (Recommended)

1. **Locate your OpenWebUI functions directory**:
   - Usually: `~/.config/open-webui/functions/` (Linux/Mac)
   - Or: `%APPDATA%\open-webui\functions\` (Windows)

2. **Copy the function files**:
   ```bash
   # Copy all function files
   cp openwebui_functions/*.py /path/to/openwebui/functions/
   ```

3. **Copy the core modules** (needed for the functions to work):
   ```bash
   # Copy the main project files to the functions directory
   cp citizen_lookup.py /path/to/openwebui/functions/
   cp org_lookup.py /path/to/openwebui/functions/
   cp galactapedia_lookup.py /path/to/openwebui/functions/
   cp simple_example.py /path/to/openwebui/functions/
   cp config.py /path/to/openwebui/functions/
   cp config.json /path/to/openwebui/functions/  # Contains your API key
   ```

#### Option B: Symbolic Links (Advanced)

Create symbolic links to keep files in sync:

```bash
# Link the functions
ln -s /path/to/StarCitizenToolsMCP/openwebui_functions/*.py /path/to/openwebui/functions/

# Link the core modules
ln -s /path/to/StarCitizenToolsMCP/citizen_lookup.py /path/to/openwebui/functions/
ln -s /path/to/StarCitizenToolsMCP/org_lookup.py /path/to/openwebui/functions/
ln -s /path/to/StarCitizenToolsMCP/galactapedia_lookup.py /path/to/openwebui/functions/
ln -s /path/to/StarCitizenToolsMCP/simple_example.py /path/to/openwebui/functions/
ln -s /path/to/StarCitizenToolsMCP/config.py /path/to/openwebui/functions/
ln -s /path/to/StarCitizenToolsMCP/config.json /path/to/openwebui/functions/
```

### Step 3: Install Dependencies

Make sure the required Python packages are available to OpenWebUI:

```bash
# Install in the same environment as OpenWebUI
pip install requests beautifulsoup4
```

### Step 4: Restart OpenWebUI

Restart OpenWebUI to load the new functions:

```bash
# If running with Docker
docker restart open-webui

# If running directly
# Stop and restart your OpenWebUI service
```

### Step 5: Enable Functions in OpenWebUI

1. **Open OpenWebUI** in your browser
2. **Go to Settings** → **Functions**
3. **Enable the Star Citizen functions**:
   - `citizen_lookup_function`
   - `organization_lookup_function` 
   - `galactapedia_function`
   - `wiki_search_function`

## 🎮 Usage Examples

Once installed, you can ask your AI assistant questions like:

### Player Lookups
```
"Look up the Star Citizen player KenzoKai"
"What's the profile of citizen TestPilot?"
"Show me information about player handle 'SpaceExplorer'"
```

### Organization Information
```
"Tell me about the Star Citizen organization HMBCREW"
"What's the member count of organization TEST?"
"Show me the members of org CORP"
```

### Lore and Universe Information
```
"Tell me about the Carrack ship in Star Citizen"
"What do you know about the Banu species?"
"Search for information about Terra in Star Citizen"
"What's the history of the UEE?"
```

### Gameplay Information
```
"How does mining work in Star Citizen?"
"What are the best trading routes?"
"Tell me about combat mechanics"
"How do I upgrade ship components?"
```

## 🔧 Available Functions

### 1. Player Profile Lookup
- **Function**: `lookup_star_citizen_player`
- **Purpose**: Get detailed player information
- **Returns**: Name, rank, enlisted date, organization, biography

### 2. Organization Lookup
- **Function**: `lookup_star_citizen_organization`
- **Purpose**: Get organization details and member lists
- **Returns**: Org info, member count, focus areas, member list

### 3. Galactapedia Search
- **Function**: `search_star_citizen_lore`
- **Purpose**: Search Star Citizen universe lore
- **Returns**: Ships, planets, species, history, organizations

### 4. Wiki Search
- **Function**: `search_star_citizen_wiki`
- **Purpose**: Find gameplay guides and information
- **Returns**: How-to guides, ship details, game mechanics

## 🛠️ Troubleshooting

### Functions Not Appearing
1. Check that files are in the correct OpenWebUI functions directory
2. Restart OpenWebUI completely
3. Check OpenWebUI logs for import errors

### Import Errors
1. Ensure all required files are copied (not just the function files)
2. Install missing Python packages: `pip install requests beautifulsoup4`
3. Check that `config.json` exists and contains your API key

### API Key Issues
1. Verify your API key is configured: `python client.py config-status`
2. Make sure `config.json` is copied to the functions directory
3. Check that the config module can find the API key

### No Results Returned
1. Test the functions directly: `python openwebui_functions/citizen_lookup_function.py`
2. Check your internet connection
3. Verify the RSI website is accessible

## 🔒 Security Notes

- Your API key is stored in `config.json` - keep this file secure
- The functions only make read-only requests to public APIs
- No sensitive data is stored or transmitted beyond the API key

## 📚 Function Reference

Each function file contains detailed documentation and examples. See:
- `openwebui_functions/citizen_lookup_function.py`
- `openwebui_functions/organization_lookup_function.py`
- `openwebui_functions/galactapedia_function.py`
- `openwebui_functions/wiki_search_function.py`

## 🎉 You're Ready!

Once everything is set up, your AI assistant will have comprehensive access to Star Citizen information and can help answer questions about players, organizations, lore, and gameplay!
