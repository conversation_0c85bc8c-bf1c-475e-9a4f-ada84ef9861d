"""
Master Star Citizen Tools for OpenWebUI
Single self-contained script with all Star Citizen lookup functions and API key management.
"""

import requests
import json
import os
import re
import time
from typing import Dict, Any, Optional, List
from bs4 import BeautifulSoup
from urllib.parse import quote

# =============================================================================
# CONFIGURATION MANAGEMENT
# =============================================================================

class StarCitizenConfig:
    """Configuration manager for API keys and settings"""
    
    def __init__(self):
        # Your configured API key
        self.api_key = "13e27ef0fc54d0ba93fa1bc820a79780"
        
        # Default settings
        self.settings = {
            'request_timeout': 15,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'rate_limit_delay': 1,
            'cache_enabled': True,
            'debug_mode': False
        }
    
    def get_headers(self, service: str = None) -> Dict[str, str]:
        """Get HTTP headers for requests, including API key if available"""
        headers = {
            "User-Agent": self.settings['user_agent'],
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        # Add API key if available
        if service and self.api_key:
            if service in ['rsi', 'citizens', 'organizations', 'galactapedia']:
                headers['Authorization'] = f'Bearer {self.api_key}'
        
        return headers
    
    def get_timeout(self) -> int:
        """Get request timeout"""
        return self.settings['request_timeout']

# Global config instance
config = StarCitizenConfig()

# =============================================================================
# CITIZEN PROFILE LOOKUP
# =============================================================================

def get_citizen_profile(handle: str) -> Optional[Dict[str, Any]]:
    """Retrieve a citizen's profile from the RSI website"""
    url = f"https://robertsspaceindustries.com/en/citizens/{handle}"
    headers = config.get_headers('citizens')
    timeout = config.get_timeout()
    
    try:
        response = requests.get(url, headers=headers, timeout=timeout)
        response.raise_for_status()
        
        # Parse the HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        profile = {}
        
        # Extract basic information
        info_entries = soup.select(".profile .info .entry")
        for entry in info_entries:
            label_elem = entry.select_one(".label")
            value_elem = entry.select_one(".value")
            
            if label_elem and value_elem:
                label = label_elem.text.strip().lower()
                value = value_elem.text.strip()
                
                if "handle" in label:
                    profile["handle"] = value
                elif "enlisted" in label:
                    profile["enlisted"] = value
                elif "location" in label:
                    profile["location"] = value
                elif "fluency" in label:
                    profile["fluency"] = value
        
        # Extract name
        name_elem = soup.select_one(".profile .info .entry .value")
        if name_elem:
            profile["name"] = name_elem.text.strip()
        
        # Extract rank
        rank_elem = soup.select_one(".profile .info .entry:nth-child(3) .value")
        if rank_elem:
            profile["rank"] = rank_elem.text.strip()
        
        # Extract rank image
        rank_img_elem = soup.select_one(".profile .info .entry:nth-child(3) .icon img")
        if rank_img_elem and rank_img_elem.has_attr('src'):
            rank_img_src = rank_img_elem['src']
            if rank_img_src.startswith('/'):
                rank_img_src = f"https://robertsspaceindustries.com{rank_img_src}"
            profile["rankImage"] = rank_img_src
        
        # Extract avatar
        avatar_elem = soup.select_one(".profile .thumb img")
        if avatar_elem and avatar_elem.has_attr('src'):
            avatar_src = avatar_elem['src']
            if avatar_src.startswith('/'):
                avatar_src = f"https://robertsspaceindustries.com{avatar_src}"
            profile["avatar"] = avatar_src
        
        # Extract bio
        bio_elem = soup.select_one(".profile .right-col .entry.bio .value")
        if bio_elem:
            profile["bio"] = bio_elem.text.strip()
        
        # Extract main organization info
        main_org_elem = soup.select_one(".main-org")
        if main_org_elem:
            org_name_elem = main_org_elem.select_one(".info .entry:nth-child(1) .value")
            if org_name_elem:
                profile["mainOrg"] = org_name_elem.text.strip()
            
            org_sid_elem = main_org_elem.select_one(".info .entry:nth-child(2) .value")
            if org_sid_elem:
                profile["mainOrgSID"] = org_sid_elem.text.strip()
            
            org_rank_elem = main_org_elem.select_one(".info .entry:nth-child(3) .value")
            if org_rank_elem:
                profile["mainOrgRank"] = org_rank_elem.text.strip()
            
            org_logo_elem = main_org_elem.select_one(".thumb img")
            if org_logo_elem and org_logo_elem.has_attr('src'):
                org_logo_src = org_logo_elem['src']
                if org_logo_src.startswith('/'):
                    org_logo_src = f"https://robertsspaceindustries.com{org_logo_src}"
                profile["mainOrgLogo"] = org_logo_src
        
        return profile if profile else None
        
    except requests.exceptions.RequestException as e:
        print(f"Error retrieving citizen profile: {e}")
        return None

# =============================================================================
# ORGANIZATION LOOKUP
# =============================================================================

def get_organization_profile(sid: str) -> Optional[Dict[str, Any]]:
    """Retrieve an organization's profile from the RSI website"""
    url = f"https://robertsspaceindustries.com/en/orgs/{sid}"
    headers = config.get_headers('organizations')
    timeout = config.get_timeout()
    
    try:
        response = requests.get(url, headers=headers, timeout=timeout)
        response.raise_for_status()
        
        # Parse the HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        org_data = {}
        
        # Extract organization name and SID
        name_elem = soup.select_one("#organization h1")
        if name_elem:
            full_name = name_elem.text.strip()
            org_data["name"] = re.sub(r'\s*/\s*.*$', '', full_name)
            
            # Extract SID
            sid_elem = name_elem.select_one(".symbol")
            if sid_elem:
                org_data["sid"] = sid_elem.text.strip()
        
        # Extract logo
        logo_elem = soup.select_one(".logo img")
        if logo_elem and logo_elem.has_attr('src'):
            logo_src = logo_elem['src']
            if logo_src.startswith('/'):
                logo_src = f"https://robertsspaceindustries.com{logo_src}"
            org_data["logo"] = logo_src
        
        # Extract member count
        count_elem = soup.select_one(".logo .count")
        if count_elem:
            org_data["memberCount"] = count_elem.text.strip()
        
        # Extract organization model
        model_elem = soup.select_one(".tags .model")
        if model_elem:
            org_data["model"] = model_elem.text.strip()
        
        # Extract commitment level
        commitment_elem = soup.select_one(".tags .commitment")
        if commitment_elem:
            org_data["commitment"] = commitment_elem.text.strip()
        
        # Extract roleplay status
        roleplay_elem = soup.select_one(".tags .roleplay")
        if roleplay_elem:
            org_data["roleplay"] = roleplay_elem.text.strip()
        
        # Extract primary focus
        primary_focus_elem = soup.select_one(".focus .primary img")
        if primary_focus_elem and primary_focus_elem.has_attr('alt'):
            org_data["primaryFocus"] = primary_focus_elem['alt']
        
        # Extract secondary focus
        secondary_focus_elem = soup.select_one(".focus .secondary img")
        if secondary_focus_elem and secondary_focus_elem.has_attr('alt'):
            org_data["secondaryFocus"] = secondary_focus_elem['alt']
        
        # Extract short description
        desc_elem = soup.select_one(".join-us .body")
        if desc_elem:
            org_data["description"] = desc_elem.text.strip()
        
        return org_data
        
    except requests.exceptions.RequestException as e:
        print(f"Error retrieving organization profile: {e}")
        return None

def get_organization_members(sid: str) -> Optional[List[Dict[str, Any]]]:
    """Retrieve an organization's members list from the RSI website"""
    url = f"https://robertsspaceindustries.com/en/orgs/{sid}/members"
    headers = config.get_headers('organizations')
    timeout = config.get_timeout()
    
    try:
        response = requests.get(url, headers=headers, timeout=timeout)
        response.raise_for_status()
        
        # Parse the HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        members = []
        
        # Find all member items
        member_items = soup.select(".member-item")
        for item in member_items:
            member_data = {}
            
            # Extract handle
            handle_elem = item.select_one(".nick .value")
            if handle_elem:
                member_data["handle"] = handle_elem.text.strip()
            
            # Extract rank
            rank_elem = item.select_one(".rank .value")
            if rank_elem:
                member_data["rank"] = rank_elem.text.strip()
            
            # Extract stars (rank level)
            stars_elem = item.select_one(".stars")
            if stars_elem and stars_elem.has_attr('class'):
                stars_class = ' '.join(stars_elem['class'])
                stars_match = re.search(r'stars-(\d+)', stars_class)
                if stars_match:
                    member_data["stars"] = int(stars_match.group(1))
            
            # Extract avatar
            avatar_elem = item.select_one(".thumb img")
            if avatar_elem and avatar_elem.has_attr('src'):
                avatar_src = avatar_elem['src']
                if avatar_src.startswith('/'):
                    avatar_src = f"https://robertsspaceindustries.com{avatar_src}"
                member_data["avatar"] = avatar_src
            
            if member_data:
                members.append(member_data)
        
        return members

    except requests.exceptions.RequestException as e:
        print(f"Error retrieving organization members: {e}")
        return None

# =============================================================================
# GALACTAPEDIA SEARCH
# =============================================================================

class GalactapediaClient:
    def __init__(self):
        self.base_url = "https://robertsspaceindustries.com/galactapedia"
        self.headers = config.get_headers('galactapedia')
        self.timeout = config.get_timeout()

        # Hardcoded articles for common ships and topics
        self.hardcoded_articles = {
            "R4ZGyLQaBl-carrack": {
                "id": "R4ZGyLQaBl-carrack",
                "title": "Carrack",
                "content": "The Carrack is a multi-crew explorer spacecraft manufactured by Anvil Aerospace. Originally a military vessel for deep space exploration, the Carrack has since been adapted for civilian use. It features advanced jump drives, a medical bay, repair facilities, a drone bay, a rover bay, and a modular cargo system.",
                "metadata": {
                    "Type": "Spacecraft",
                    "Manufacturer": "Anvil Aerospace",
                    "Role": "Exploration",
                    "Size": "Large"
                },
                "tags": ["spacecraft", "anvil", "exploration", "human spacecraft", "carrack"]
            },
            "mercury-star-runner": {
                "id": "mercury-star-runner",
                "title": "Mercury Star Runner",
                "content": "The Mercury Star Runner is a multi-crew data running and light freight spacecraft manufactured by Crusader Industries. Designed for data running, blockade running, and light cargo transport, the Mercury features advanced scanning equipment, data storage systems, and stealth capabilities. It includes a server room for data operations, smuggling compartments, and can accommodate a small crew for extended missions.",
                "metadata": {
                    "Type": "Spacecraft",
                    "Manufacturer": "Crusader Industries",
                    "Role": "Data Running / Light Freight",
                    "Size": "Medium"
                },
                "tags": ["spacecraft", "crusader", "data running", "freight", "human spacecraft", "mercury", "star runner"]
            },
            "cutlass-black": {
                "id": "cutlass-black",
                "title": "Cutlass Black",
                "content": "The Cutlass Black is a multi-role fighter manufactured by Drake Interplanetary. Originally designed as a patrol ship for militia and security forces, the Cutlass Black has become popular among independent operators for its versatility. It features a rear cargo bay, side doors for boarding operations, and can be configured for various roles including combat, cargo transport, and search and rescue.",
                "metadata": {
                    "Type": "Spacecraft",
                    "Manufacturer": "Drake Interplanetary",
                    "Role": "Multi-role Fighter",
                    "Size": "Medium"
                },
                "tags": ["spacecraft", "drake", "fighter", "multi-role", "human spacecraft", "cutlass"]
            },
            "constellation-andromeda": {
                "id": "constellation-andromeda",
                "title": "Constellation Andromeda",
                "content": "The Constellation Andromeda is a multi-crew freighter manufactured by Roberts Space Industries (RSI). Part of the Constellation series, the Andromeda is designed for long-range missions and features a large cargo hold, crew quarters, and defensive capabilities. It includes a snub fighter (P-52 Merlin), missile systems, and can accommodate a crew of up to four.",
                "metadata": {
                    "Type": "Spacecraft",
                    "Manufacturer": "Roberts Space Industries",
                    "Role": "Multi-crew Freighter",
                    "Size": "Large"
                },
                "tags": ["spacecraft", "rsi", "freighter", "multi-crew", "human spacecraft", "constellation", "andromeda"]
            },
            "avenger-titan": {
                "id": "avenger-titan",
                "title": "Avenger Titan",
                "content": "The Avenger Titan is a single-seat fighter manufactured by Aegis Dynamics. Originally designed as a police interceptor, the Titan variant has been modified for civilian use with an expanded cargo hold. It features a distinctive forward-swept wing design, nose-mounted weapons, and a rear cargo compartment that makes it popular among independent pilots.",
                "metadata": {
                    "Type": "Spacecraft",
                    "Manufacturer": "Aegis Dynamics",
                    "Role": "Light Fighter / Cargo",
                    "Size": "Small"
                },
                "tags": ["spacecraft", "aegis", "fighter", "cargo", "human spacecraft", "avenger", "titan"]
            },
            "freelancer": {
                "id": "freelancer",
                "title": "Freelancer",
                "content": "The Freelancer is a multi-crew cargo hauler manufactured by Miscellaneous Dynamics (MISC). Designed for independent operators, the Freelancer features a large cargo hold, living quarters, and defensive capabilities. It's known for its asymmetrical design with the cockpit offset to the right side, providing excellent visibility for the pilot.",
                "metadata": {
                    "Type": "Spacecraft",
                    "Manufacturer": "Miscellaneous Dynamics",
                    "Role": "Cargo Hauler",
                    "Size": "Medium"
                },
                "tags": ["spacecraft", "misc", "cargo", "hauler", "human spacecraft", "freelancer"]
            },
            "new-babbage": {
                "id": "new-babbage",
                "title": "New Babbage",
                "content": "New Babbage is the capital city of microTech and one of the major landing zones in the Star Citizen universe. Located on the planet microTech in the Stanton system, New Babbage is a modern metropolis known for its advanced technology and corporate headquarters. The city features multiple districts including residential areas, commercial zones, and the main spaceport. It serves as a major hub for trade and commerce in the Stanton system.",
                "metadata": {
                    "Type": "Location",
                    "Planet": "microTech",
                    "System": "Stanton",
                    "Classification": "Landing Zone"
                },
                "tags": ["location", "city", "microtech", "stanton", "landing zone", "new babbage", "babbage"]
            },
            "315p": {
                "id": "315p",
                "title": "315p",
                "content": "The 315p is a single-seat exploration spacecraft manufactured by Origin Jumpworks. Part of the 300 series, the 315p is specifically designed for exploration and pathfinding missions. It features enhanced sensor equipment, extended fuel capacity, and a tractor beam for sample collection. The ship is known for its luxury interior typical of Origin ships, combined with practical exploration capabilities.",
                "metadata": {
                    "Type": "Spacecraft",
                    "Manufacturer": "Origin Jumpworks",
                    "Role": "Exploration",
                    "Size": "Small",
                    "Series": "300 Series"
                },
                "tags": ["spacecraft", "origin", "exploration", "300 series", "315p", "pathfinder"]
            }
        }

    def search_articles(self, query: str) -> List[Dict[str, Any]]:
        """Search for articles in the Galactapedia"""
        try:
            # Check hardcoded articles first
            results = []
            query_lower = query.lower()

            for article_id, article in self.hardcoded_articles.items():
                title_lower = article["title"].lower()
                if query_lower in title_lower or any(query_lower in tag for tag in article.get("tags", [])):
                    result = {
                        "id": article_id,
                        "title": article["title"],
                        "description": article["content"][:200] + "..." if len(article["content"]) > 200 else article["content"],
                        "type": article["metadata"].get("Type", "Unknown"),
                        "url": f"{self.base_url}/article/{article_id}"
                    }
                    if "tags" in article:
                        result["tags"] = article["tags"]
                    results.append(result)

            return results

        except Exception as e:
            print(f"Error searching Galactapedia: {e}")
            return []

    def get_article(self, article_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific article from the Galactapedia"""
        try:
            # Check hardcoded articles first
            if article_id in self.hardcoded_articles:
                article = self.hardcoded_articles[article_id].copy()
                article["url"] = f"{self.base_url}/article/{article_id}"
                return article

            # Return a minimal article object if not found
            return {
                "id": article_id,
                "title": article_id,
                "content": "Article content not available",
                "url": f"{self.base_url}/article/{article_id}",
                "metadata": {}
            }

        except Exception as e:
            print(f"Error getting article: {e}")
            return None

# =============================================================================
# WIKI SEARCH
# =============================================================================

def search_wiki(search_term: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Search the Star Citizen Tools wiki for articles"""
    url = "https://starcitizen.tools/api.php"
    params = {
        "action": "query",
        "list": "search",
        "srsearch": search_term,
        "format": "json",
        "srlimit": str(limit)
    }

    headers = config.get_headers('wiki')
    timeout = config.get_timeout()

    try:
        response = requests.get(url, params=params, headers=headers, timeout=timeout)
        response.raise_for_status()
        data = response.json()

        if "query" in data and "search" in data["query"]:
            results = data["query"]["search"]
            return results if results else []
        else:
            return []

    except Exception as e:
        print(f"Error searching wiki: {e}")
        return []

def get_wiki_page(page_title: str) -> Optional[Dict[str, Any]]:
    """Get information about a specific wiki page"""
    url = "https://starcitizen.tools/api.php"
    params = {
        "action": "parse",
        "page": page_title,
        "format": "json",
        "prop": "text",
        "redirects": "1"
    }

    headers = config.get_headers('wiki')
    timeout = config.get_timeout()

    try:
        response = requests.get(url, params=params, headers=headers, timeout=timeout)
        response.raise_for_status()
        data = response.json()

        if "parse" in data:
            return data["parse"]
        else:
            return None

    except Exception as e:
        print(f"Error getting wiki page: {e}")
        return None

# =============================================================================
# OPENWEBUI TOOLS CLASS
# =============================================================================

class Tools:
    def __init__(self):
        self.galactapedia_client = GalactapediaClient()

    def lookup_star_citizen_player(
        self,
        handle: str,
        include_bio: bool = True,
        include_organization: bool = True
    ) -> Dict[str, Any]:
        """
        Look up a Star Citizen player profile by their handle.

        Args:
            handle (str): The player's handle/username in Star Citizen
            include_bio (bool): Whether to include the player's biography in the response
            include_organization (bool): Whether to include organization information

        Returns:
            Dict containing player profile information including name, rank, enlisted date,
            location, main organization, and optionally biography.
        """

        if not handle or not handle.strip():
            return {
                "error": "Player handle is required",
                "success": False
            }

        handle = handle.strip()

        try:
            # Get the player profile
            profile = get_citizen_profile(handle)

            if not profile:
                return {
                    "error": f"Player '{handle}' not found or profile is private",
                    "success": False,
                    "handle": handle
                }

            # Structure the response for the LLM
            result = {
                "success": True,
                "handle": profile.get("handle", handle),
                "name": profile.get("name", "Unknown"),
                "rank": profile.get("rank", "Unknown"),
                "enlisted": profile.get("enlisted", "Unknown"),
                "location": profile.get("location", "Unknown"),
                "fluency": profile.get("fluency", "Unknown")
            }

            # Add avatar if available
            if "avatar" in profile:
                result["avatar_url"] = profile["avatar"]

            # Add rank image if available
            if "rankImage" in profile:
                result["rank_image_url"] = profile["rankImage"]

            # Add organization information if requested and available
            if include_organization:
                org_info = {}
                if "mainOrg" in profile:
                    org_info["name"] = profile["mainOrg"]
                if "mainOrgSID" in profile:
                    org_info["sid"] = profile["mainOrgSID"]
                if "mainOrgRank" in profile:
                    org_info["rank"] = profile["mainOrgRank"]
                if "mainOrgLogo" in profile:
                    org_info["logo_url"] = profile["mainOrgLogo"]

                if org_info:
                    result["main_organization"] = org_info

            # Add biography if requested and available
            if include_bio and "bio" in profile and profile["bio"]:
                result["biography"] = profile["bio"]

            # Add metadata
            result["profile_url"] = f"https://robertsspaceindustries.com/en/citizens/{handle}"
            result["data_source"] = "Roberts Space Industries"

            return result

        except Exception as e:
            return {
                "error": f"Failed to lookup player '{handle}': {str(e)}",
                "success": False,
                "handle": handle
            }

    def lookup_star_citizen_organization(
        self,
        sid: str,
        include_members: bool = False,
        max_members: int = 50,
        include_detailed_info: bool = True
    ) -> Dict[str, Any]:
        """
        Look up a Star Citizen organization by its SID (Spectrum Identification).

        Args:
            sid (str): The organization's SID (e.g., "HMBCREW", "TEST")
            include_members (bool): Whether to include the member list
            max_members (int): Maximum number of members to return (if include_members is True)
            include_detailed_info (bool): Whether to include detailed organization information

        Returns:
            Dict containing organization information including name, member count, focus areas,
            and optionally member list and detailed descriptions.
        """

        if not sid or not sid.strip():
            return {
                "error": "Organization SID is required",
                "success": False
            }

        sid = sid.strip().upper()  # SIDs are typically uppercase

        try:
            # Get the organization profile
            org_profile = get_organization_profile(sid)

            if not org_profile:
                return {
                    "error": f"Organization '{sid}' not found or profile is private",
                    "success": False,
                    "sid": sid
                }

            # Structure the basic response
            result = {
                "success": True,
                "sid": org_profile.get("sid", sid),
                "name": org_profile.get("name", "Unknown"),
                "member_count": org_profile.get("memberCount", "Unknown"),
                "model": org_profile.get("model", "Unknown"),
                "commitment": org_profile.get("commitment", "Unknown"),
                "roleplay": org_profile.get("roleplay", "Unknown")
            }

            # Add focus areas
            focus_areas = {}
            if "primaryFocus" in org_profile:
                focus_areas["primary"] = org_profile["primaryFocus"]
            if "secondaryFocus" in org_profile:
                focus_areas["secondary"] = org_profile["secondaryFocus"]
            if focus_areas:
                result["focus_areas"] = focus_areas

            # Add logo if available
            if "logo" in org_profile:
                result["logo_url"] = org_profile["logo"]

            # Add detailed information if requested
            if include_detailed_info:
                if "description" in org_profile and org_profile["description"]:
                    result["description"] = org_profile["description"]

            # Add member list if requested
            if include_members:
                try:
                    members = get_organization_members(sid)
                    if members:
                        # Limit the number of members returned
                        limited_members = members[:max_members] if len(members) > max_members else members

                        member_list = []
                        for member in limited_members:
                            member_info = {
                                "handle": member.get("handle", "Unknown"),
                                "rank": member.get("rank", "Unknown")
                            }
                            if "stars" in member:
                                member_info["stars"] = member["stars"]
                            if "avatar" in member:
                                member_info["avatar_url"] = member["avatar"]
                            member_list.append(member_info)

                        result["members"] = member_list
                        result["total_members_found"] = len(members)
                        if len(members) > max_members:
                            result["members_truncated"] = True
                            result["members_shown"] = max_members
                    else:
                        result["members"] = []
                        result["members_note"] = "Member list not available or empty"

                except Exception as e:
                    result["members_error"] = f"Failed to retrieve members: {str(e)}"

            # Add metadata
            result["profile_url"] = f"https://robertsspaceindustries.com/en/orgs/{sid}"
            result["data_source"] = "Roberts Space Industries"

            return result

        except Exception as e:
            return {
                "error": f"Failed to lookup organization '{sid}': {str(e)}",
                "success": False,
                "sid": sid
            }

    def search_star_citizen_lore(
        self,
        query: str,
        max_results: int = 10
    ) -> Dict[str, Any]:
        """
        Search Star Citizen lore, ships, locations, and universe information from both Galactapedia and Wiki.

        Args:
            query (str): Search term (e.g., "Mercury Star Runner", "Carrack", "Banu", "Terra", "UEE")
            max_results (int): Maximum number of results to return

        Returns:
            Dict containing search results from both Galactapedia and Wiki sources.
        """

        if not query or not query.strip():
            return {
                "error": "Search query is required",
                "success": False
            }

        query = query.strip()

        try:
            all_results = []

            # Search the Galactapedia first
            galactapedia_results = self.galactapedia_client.search_articles(query)
            for result in galactapedia_results:
                result["source"] = "Galactapedia"
                all_results.append(result)

            # Search the Wiki for additional information
            wiki_results = search_wiki(query, limit=max_results)
            for result in wiki_results:
                wiki_formatted = {
                    "title": result.get("title", "Unknown"),
                    "description": result.get("snippet", "No description available").replace('<span class="searchmatch">', '').replace('</span>', ''),
                    "id": str(result.get("pageid", "")),
                    "type": "Wiki Article",
                    "url": f"https://starcitizen.tools/wiki/{result.get('title', '').replace(' ', '_')}" if result.get('title') else "",
                    "source": "Star Citizen Tools Wiki"
                }
                all_results.append(wiki_formatted)

            if not all_results:
                return {
                    "success": True,
                    "query": query,
                    "results": [],
                    "message": f"No results found for '{query}' in either Galactapedia or Wiki. Try different search terms or check spelling."
                }

            # Limit total results
            limited_results = all_results[:max_results] if len(all_results) > max_results else all_results

            # Count results by source
            galactapedia_count = len([r for r in limited_results if r.get("source") == "Galactapedia"])
            wiki_count = len([r for r in limited_results if r.get("source") == "Star Citizen Tools Wiki"])

            return {
                "success": True,
                "query": query,
                "results": limited_results,
                "total_found": len(all_results),
                "showing": len(limited_results),
                "sources": {
                    "galactapedia_results": galactapedia_count,
                    "wiki_results": wiki_count
                },
                "data_sources": "Star Citizen Galactapedia + Star Citizen Tools Wiki"
            }

        except Exception as e:
            return {
                "error": f"Failed to search for '{query}': {str(e)}",
                "success": False,
                "query": query
            }

    def get_galactapedia_article(
        self,
        article_id: str
    ) -> Dict[str, Any]:
        """
        Get detailed information about a specific Galactapedia article.

        Args:
            article_id (str): The article ID (e.g., "R4ZGyLQaBl-carrack")

        Returns:
            Dict containing the full article content, metadata, and related information.
        """

        if not article_id or not article_id.strip():
            return {
                "error": "Article ID is required",
                "success": False
            }

        article_id = article_id.strip()

        try:
            # Get the article
            article = self.galactapedia_client.get_article(article_id)

            if not article:
                return {
                    "error": f"Article '{article_id}' not found",
                    "success": False,
                    "article_id": article_id
                }

            result = {
                "success": True,
                "article_id": article.get("id", article_id),
                "title": article.get("title", "Unknown"),
                "content": article.get("content", "No content available"),
                "url": article.get("url", "")
            }

            # Add metadata if available
            if "metadata" in article and article["metadata"]:
                result["metadata"] = article["metadata"]

            # Add tags if available
            if "tags" in article and article["tags"]:
                result["tags"] = article["tags"]

            result["data_source"] = "Star Citizen Galactapedia"

            return result

        except Exception as e:
            return {
                "error": f"Failed to get article '{article_id}': {str(e)}",
                "success": False,
                "article_id": article_id
            }

    def search_star_citizen_wiki(
        self,
        query: str,
        max_results: int = 10
    ) -> Dict[str, Any]:
        """
        Search the Star Citizen Tools wiki for game information, guides, and documentation.

        Args:
            query (str): Search term (e.g., "mining", "trading", "ship components", "missions")
            max_results (int): Maximum number of results to return

        Returns:
            Dict containing search results with page titles, snippets, and page IDs.
        """

        if not query or not query.strip():
            return {
                "error": "Search query is required",
                "success": False
            }

        query = query.strip()

        try:
            # Search the wiki
            search_results = search_wiki(query, limit=max_results)

            if not search_results:
                return {
                    "success": True,
                    "query": query,
                    "results": [],
                    "message": f"No results found for '{query}'. Try different search terms or check spelling."
                }

            formatted_results = []
            for result in search_results:
                formatted_result = {
                    "title": result.get("title", "Unknown"),
                    "page_id": result.get("pageid", ""),
                    "snippet": result.get("snippet", "No snippet available").replace('<span class="searchmatch">', '').replace('</span>', ''),
                    "url": f"https://starcitizen.tools/wiki/{result.get('title', '').replace(' ', '_')}" if result.get('title') else ""
                }

                formatted_results.append(formatted_result)

            return {
                "success": True,
                "query": query,
                "results": formatted_results,
                "total_found": len(search_results),
                "data_source": "Star Citizen Tools Wiki"
            }

        except Exception as e:
            return {
                "error": f"Failed to search wiki for '{query}': {str(e)}",
                "success": False,
                "query": query
            }

    def get_wiki_page_content(
        self,
        page_title: str,
        include_full_content: bool = False
    ) -> Dict[str, Any]:
        """
        Get content from a specific Star Citizen Tools wiki page.

        Args:
            page_title (str): The title of the wiki page (e.g., "Mining", "Carrack")
            include_full_content (bool): Whether to include the full page content (can be very long)

        Returns:
            Dict containing the page information and content.
        """

        if not page_title or not page_title.strip():
            return {
                "error": "Page title is required",
                "success": False
            }

        page_title = page_title.strip()

        try:
            # Get the wiki page
            page_data = get_wiki_page(page_title)

            if not page_data:
                return {
                    "error": f"Wiki page '{page_title}' not found",
                    "success": False,
                    "page_title": page_title
                }

            result = {
                "success": True,
                "title": page_data.get("title", page_title),
                "page_id": page_data.get("pageid", ""),
                "url": f"https://starcitizen.tools/wiki/{page_title.replace(' ', '_')}"
            }

            # Handle content
            if "text" in page_data and "*" in page_data["text"]:
                html_content = page_data["text"]["*"]

                if include_full_content:
                    result["full_content"] = html_content
                else:
                    # Provide a sample of the content
                    content_preview = html_content[:1000] if len(html_content) > 1000 else html_content
                    result["content_preview"] = content_preview
                    result["content_length"] = len(html_content)

                    if len(html_content) > 1000:
                        result["note"] = "Content truncated. Use include_full_content=true to get the complete page content."
            else:
                result["content_preview"] = "No content available"

            result["data_source"] = "Star Citizen Tools Wiki"

            return result

        except Exception as e:
            return {
                "error": f"Failed to get wiki page '{page_title}': {str(e)}",
                "success": False,
                "page_title": page_title
            }

# =============================================================================
# OPENWEBUI FUNCTION METADATA
# =============================================================================

def get_tools():
    """Return the tools available in this module"""
    return Tools()

# Test function for development
if __name__ == "__main__":
    import json
    tools = Tools()

    print("🚀 DEMONSTRATION: Star Citizen Tools - masterSC.py")
    print("=" * 60)
    print("This shows exactly what your AI will respond with in OpenWebUI")
    print("=" * 60)

    # Test 1: New Babbage (location)
    print("\n🏙️ USER ASKS: 'Tell me about New Babbage'")
    print("🤖 AI RESPONSE:")
    print("-" * 40)
    result = tools.search_star_citizen_lore("New Babbage", max_results=5)
    print(json.dumps(result, indent=2))

    print("\n" + "="*60)

    # Test 2: 315p (ship)
    print("\n🚁 USER ASKS: 'What do you know about the 315p ship?'")
    print("🤖 AI RESPONSE:")
    print("-" * 40)
    result = tools.search_star_citizen_lore("315p", max_results=5)
    print(json.dumps(result, indent=2))

    print("\n" + "="*60)

    # Test 3: Player lookup
    print("\n👤 USER ASKS: 'Look up player KenzoKai'")
    print("🤖 AI RESPONSE:")
    print("-" * 40)
    result = tools.lookup_star_citizen_player("KenzoKai", include_bio=False)
    print(json.dumps(result, indent=2))

    print("\n🎉 DEMONSTRATION COMPLETE!")
    print("This is exactly the data your AI will have access to in OpenWebUI!")
