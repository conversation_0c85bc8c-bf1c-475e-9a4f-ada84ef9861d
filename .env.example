# Star Citizen Tools MCP Server Configuration
# Copy this file to .env and fill in your API keys and settings

# =============================================================================
# API KEYS
# =============================================================================

# RSI (Roberts Space Industries) API Key
# Used for accessing RSI citizen profiles, organizations, and Galactapedia
# Get your API key from: https://robertsspaceindustries.com/account/settings
RSI_API_KEY=your_rsi_api_key_here

# Star Citizen Tools Wiki API Key (if required)
# Used for accessing the Star Citizen Tools wiki
# Most wiki operations don't require an API key, but having one may increase rate limits
WIKI_API_KEY=your_wiki_api_key_here

# Galactapedia API Key (if different from RSI)
# Used specifically for Galactapedia operations
GALACTAPEDIA_API_KEY=your_galactapedia_api_key_here

# Citizens API Key (if different from RSI)
# Used specifically for citizen profile operations
CITIZENS_API_KEY=your_citizens_api_key_here

# Organizations API Key (if different from RSI)
# Used specifically for organization profile operations
ORGANIZATIONS_API_KEY=your_organizations_api_key_here

# =============================================================================
# REQUEST SETTINGS
# =============================================================================

# Request timeout in seconds (default: 15)
REQUEST_TIMEOUT=15

# Custom User-Agent string for HTTP requests
# Leave empty to use the default browser user agent
USER_AGENT=

# Rate limiting delay between requests in seconds (default: 1)
# Helps prevent being blocked by rate limiting
RATE_LIMIT_DELAY=1

# =============================================================================
# CACHING SETTINGS
# =============================================================================

# Enable response caching (true/false, default: true)
# Caches API responses to improve performance and reduce API calls
CACHE_ENABLED=true

# =============================================================================
# DEBUG SETTINGS
# =============================================================================

# Enable debug mode (true/false, default: false)
# Shows additional logging and debug information
DEBUG_MODE=false

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env in the same directory
# 2. Fill in your API keys (at minimum RSI_API_KEY if you have one)
# 3. Adjust settings as needed
# 4. The application will automatically load these settings

# Note: Most Star Citizen data sources don't require API keys for basic access,
# but having them may provide:
# - Higher rate limits
# - Access to additional features
# - Better reliability

# Security Note: Never commit your .env file to version control!
# The .env file should be added to your .gitignore file.
