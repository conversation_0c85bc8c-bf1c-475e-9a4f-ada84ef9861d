"""
OpenWebUI Function: Star Citizen Organization Lookup
Allows users to look up Star Citizen organization profiles and member lists.
"""

import json
import sys
import os
from typing import Dict, Any, Optional, List

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from org_lookup import get_organization_profile, get_organization_members
    from config import get_config
    MODULES_AVAILABLE = True
except ImportError as e:
    MODULES_AVAILABLE = False
    import_error = str(e)


class Tools:
    def __init__(self):
        pass

    def lookup_star_citizen_organization(
        self,
        sid: str,
        include_members: bool = False,
        max_members: int = 50,
        include_detailed_info: bool = True
    ) -> Dict[str, Any]:
        """
        Look up a Star Citizen organization by its SID (Spectrum Identification).
        
        Args:
            sid (str): The organization's SID (e.g., "HMBCREW", "TEST")
            include_members (bool): Whether to include the member list
            max_members (int): Maximum number of members to return (if include_members is True)
            include_detailed_info (bool): Whether to include detailed organization information
            
        Returns:
            Dict containing organization information including name, member count, focus areas,
            and optionally member list and detailed descriptions.
        """
        
        if not MODULES_AVAILABLE:
            return {
                "error": f"Required modules not available: {import_error}",
                "success": False
            }
        
        if not sid or not sid.strip():
            return {
                "error": "Organization SID is required",
                "success": False
            }
        
        sid = sid.strip().upper()  # SIDs are typically uppercase
        
        try:
            # Get the organization profile
            org_profile = get_organization_profile(sid)
            
            if not org_profile:
                return {
                    "error": f"Organization '{sid}' not found or profile is private",
                    "success": False,
                    "sid": sid
                }
            
            # Structure the basic response
            result = {
                "success": True,
                "sid": org_profile.get("sid", sid),
                "name": org_profile.get("name", "Unknown"),
                "member_count": org_profile.get("memberCount", "Unknown"),
                "model": org_profile.get("model", "Unknown"),
                "commitment": org_profile.get("commitment", "Unknown"),
                "roleplay": org_profile.get("roleplay", "Unknown")
            }
            
            # Add focus areas
            focus_areas = {}
            if "primaryFocus" in org_profile:
                focus_areas["primary"] = org_profile["primaryFocus"]
            if "secondaryFocus" in org_profile:
                focus_areas["secondary"] = org_profile["secondaryFocus"]
            if focus_areas:
                result["focus_areas"] = focus_areas
            
            # Add media URLs
            media = {}
            if "logo" in org_profile:
                media["logo"] = org_profile["logo"]
            if "banner" in org_profile:
                media["banner"] = org_profile["banner"]
            if "background" in org_profile:
                media["background"] = org_profile["background"]
            if "cover" in org_profile:
                media["cover"] = org_profile["cover"]
            if media:
                result["media"] = media
            
            # Add detailed information if requested
            if include_detailed_info:
                if "description" in org_profile and org_profile["description"]:
                    result["description"] = org_profile["description"]
                
                # Add available detailed sections (but not the full content to avoid overwhelming the LLM)
                detailed_sections = {}
                if "history" in org_profile and org_profile["history"]:
                    detailed_sections["history"] = "Available (use get_organization_details for full text)"
                if "manifesto" in org_profile and org_profile["manifesto"]:
                    detailed_sections["manifesto"] = "Available (use get_organization_details for full text)"
                if "charter" in org_profile and org_profile["charter"]:
                    detailed_sections["charter"] = "Available (use get_organization_details for full text)"
                
                if detailed_sections:
                    result["detailed_sections"] = detailed_sections
            
            # Add member list if requested
            if include_members:
                try:
                    members = get_organization_members(sid)
                    if members:
                        # Limit the number of members returned
                        limited_members = members[:max_members] if len(members) > max_members else members
                        
                        member_list = []
                        for member in limited_members:
                            member_info = {
                                "handle": member.get("handle", "Unknown"),
                                "rank": member.get("rank", "Unknown")
                            }
                            if "stars" in member:
                                member_info["stars"] = member["stars"]
                            if "avatar" in member:
                                member_info["avatar_url"] = member["avatar"]
                            member_list.append(member_info)
                        
                        result["members"] = member_list
                        result["total_members_found"] = len(members)
                        if len(members) > max_members:
                            result["members_truncated"] = True
                            result["members_shown"] = max_members
                    else:
                        result["members"] = []
                        result["members_note"] = "Member list not available or empty"
                        
                except Exception as e:
                    result["members_error"] = f"Failed to retrieve members: {str(e)}"
            
            # Add metadata
            result["profile_url"] = f"https://robertsspaceindustries.com/en/orgs/{sid}"
            result["data_source"] = "Roberts Space Industries"
            
            return result
            
        except Exception as e:
            return {
                "error": f"Failed to lookup organization '{sid}': {str(e)}",
                "success": False,
                "sid": sid
            }

    def get_organization_details(
        self,
        sid: str,
        section: str = "all"
    ) -> Dict[str, Any]:
        """
        Get detailed information (history, manifesto, charter) for a Star Citizen organization.
        
        Args:
            sid (str): The organization's SID
            section (str): Which section to retrieve ("history", "manifesto", "charter", or "all")
            
        Returns:
            Dict containing the requested detailed information.
        """
        
        if not MODULES_AVAILABLE:
            return {
                "error": f"Required modules not available: {import_error}",
                "success": False
            }
        
        if not sid or not sid.strip():
            return {
                "error": "Organization SID is required",
                "success": False
            }
        
        sid = sid.strip().upper()
        valid_sections = ["history", "manifesto", "charter", "all"]
        
        if section not in valid_sections:
            return {
                "error": f"Invalid section '{section}'. Must be one of: {', '.join(valid_sections)}",
                "success": False
            }
        
        try:
            # Get the organization profile
            org_profile = get_organization_profile(sid)
            
            if not org_profile:
                return {
                    "error": f"Organization '{sid}' not found",
                    "success": False,
                    "sid": sid
                }
            
            result = {
                "success": True,
                "sid": org_profile.get("sid", sid),
                "name": org_profile.get("name", "Unknown")
            }
            
            # Add requested sections
            if section == "all" or section == "history":
                if "history" in org_profile and org_profile["history"]:
                    result["history"] = org_profile["history"]
                else:
                    result["history"] = "No history information available"
            
            if section == "all" or section == "manifesto":
                if "manifesto" in org_profile and org_profile["manifesto"]:
                    result["manifesto"] = org_profile["manifesto"]
                else:
                    result["manifesto"] = "No manifesto available"
            
            if section == "all" or section == "charter":
                if "charter" in org_profile and org_profile["charter"]:
                    result["charter"] = org_profile["charter"]
                else:
                    result["charter"] = "No charter available"
            
            return result
            
        except Exception as e:
            return {
                "error": f"Failed to get details for organization '{sid}': {str(e)}",
                "success": False,
                "sid": sid
            }


# OpenWebUI Function Metadata
def get_tools():
    """Return the tools available in this module"""
    return Tools()


# Function definitions for OpenWebUI
FUNCTION_DEFINITIONS = [
    {
        "name": "lookup_star_citizen_organization",
        "description": "Look up a Star Citizen organization by its SID. Returns organization information including name, member count, focus areas, and optionally member list.",
        "parameters": {
            "type": "object",
            "properties": {
                "sid": {
                    "type": "string",
                    "description": "The organization's SID (Spectrum Identification), e.g., 'HMBCREW', 'TEST'"
                },
                "include_members": {
                    "type": "boolean",
                    "description": "Whether to include the member list",
                    "default": False
                },
                "max_members": {
                    "type": "integer",
                    "description": "Maximum number of members to return",
                    "default": 50
                },
                "include_detailed_info": {
                    "type": "boolean",
                    "description": "Whether to include detailed organization information",
                    "default": True
                }
            },
            "required": ["sid"]
        }
    },
    {
        "name": "get_organization_details",
        "description": "Get detailed information (history, manifesto, charter) for a Star Citizen organization.",
        "parameters": {
            "type": "object",
            "properties": {
                "sid": {
                    "type": "string",
                    "description": "The organization's SID"
                },
                "section": {
                    "type": "string",
                    "description": "Which section to retrieve",
                    "enum": ["history", "manifesto", "charter", "all"],
                    "default": "all"
                }
            },
            "required": ["sid"]
        }
    }
]


# Test function for development
if __name__ == "__main__":
    tools = Tools()
    
    # Test with a known organization
    test_sid = "HMBCREW"
    print(f"Testing lookup for organization: {test_sid}")
    
    result = tools.lookup_star_citizen_organization(test_sid, include_members=True, max_members=10)
    print(json.dumps(result, indent=2))
