"""
OpenWebUI Function: Star Citizen Tools Wiki Search
Allows users to search the Star Citizen Tools wiki for game information and guides.
"""

import json
import sys
import os
from typing import Dict, Any, Optional, List

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from simple_example import search_wiki, get_wiki_page
    from config import get_config
    MODULES_AVAILABLE = True
except ImportError as e:
    MODULES_AVAILABLE = False
    import_error = str(e)


class Tools:
    def __init__(self):
        pass

    def search_star_citizen_wiki(
        self,
        query: str,
        max_results: int = 10
    ) -> Dict[str, Any]:
        """
        Search the Star Citizen Tools wiki for game information, guides, and documentation.
        
        Args:
            query (str): Search term (e.g., "mining", "trading", "ship components", "missions")
            max_results (int): Maximum number of results to return
            
        Returns:
            Dict containing search results with page titles, snippets, and page IDs.
        """
        
        if not MODULES_AVAILABLE:
            return {
                "error": f"Required modules not available: {import_error}",
                "success": False
            }
        
        if not query or not query.strip():
            return {
                "error": "Search query is required",
                "success": False
            }
        
        query = query.strip()
        
        try:
            # Search the wiki
            search_results = search_wiki(query, limit=max_results)
            
            if not search_results:
                return {
                    "success": True,
                    "query": query,
                    "results": [],
                    "message": f"No results found for '{query}'. Try different search terms or check spelling."
                }
            
            formatted_results = []
            for result in search_results:
                formatted_result = {
                    "title": result.get("title", "Unknown"),
                    "page_id": result.get("pageid", ""),
                    "snippet": result.get("snippet", "No snippet available").replace('<span class="searchmatch">', '').replace('</span>', ''),
                    "url": f"https://starcitizen.tools/wiki/{result.get('title', '').replace(' ', '_')}" if result.get('title') else ""
                }
                
                formatted_results.append(formatted_result)
            
            return {
                "success": True,
                "query": query,
                "results": formatted_results,
                "total_found": len(search_results),
                "data_source": "Star Citizen Tools Wiki"
            }
            
        except Exception as e:
            return {
                "error": f"Failed to search wiki for '{query}': {str(e)}",
                "success": False,
                "query": query
            }

    def get_wiki_page_content(
        self,
        page_title: str,
        include_full_content: bool = False
    ) -> Dict[str, Any]:
        """
        Get content from a specific Star Citizen Tools wiki page.
        
        Args:
            page_title (str): The title of the wiki page (e.g., "Mining", "Carrack")
            include_full_content (bool): Whether to include the full page content (can be very long)
            
        Returns:
            Dict containing the page information and content.
        """
        
        if not MODULES_AVAILABLE:
            return {
                "error": f"Required modules not available: {import_error}",
                "success": False
            }
        
        if not page_title or not page_title.strip():
            return {
                "error": "Page title is required",
                "success": False
            }
        
        page_title = page_title.strip()
        
        try:
            # Get the wiki page
            page_data = get_wiki_page(page_title)
            
            if not page_data:
                return {
                    "error": f"Wiki page '{page_title}' not found",
                    "success": False,
                    "page_title": page_title
                }
            
            result = {
                "success": True,
                "title": page_data.get("title", page_title),
                "page_id": page_data.get("pageid", ""),
                "url": f"https://starcitizen.tools/wiki/{page_title.replace(' ', '_')}"
            }
            
            # Handle content
            if "text" in page_data and "*" in page_data["text"]:
                html_content = page_data["text"]["*"]
                
                if include_full_content:
                    result["full_content"] = html_content
                else:
                    # Provide a sample of the content
                    content_preview = html_content[:1000] if len(html_content) > 1000 else html_content
                    result["content_preview"] = content_preview
                    result["content_length"] = len(html_content)
                    
                    if len(html_content) > 1000:
                        result["note"] = "Content truncated. Use include_full_content=true to get the complete page content."
            else:
                result["content_preview"] = "No content available"
            
            result["data_source"] = "Star Citizen Tools Wiki"
            
            return result
            
        except Exception as e:
            return {
                "error": f"Failed to get wiki page '{page_title}': {str(e)}",
                "success": False,
                "page_title": page_title
            }

    def get_star_citizen_game_info(
        self,
        topic: str
    ) -> Dict[str, Any]:
        """
        Get comprehensive information about a Star Citizen game topic by searching both wiki and Galactapedia.
        
        Args:
            topic (str): Game topic (e.g., "mining", "trading", "combat", "exploration")
            
        Returns:
            Dict containing information from multiple sources about the topic.
        """
        
        if not MODULES_AVAILABLE:
            return {
                "error": f"Required modules not available: {import_error}",
                "success": False
            }
        
        if not topic or not topic.strip():
            return {
                "error": "Topic is required",
                "success": False
            }
        
        topic = topic.strip()
        
        try:
            # Search the wiki for practical information
            wiki_results = self.search_star_citizen_wiki(topic, max_results=5)
            
            result = {
                "success": True,
                "topic": topic,
                "wiki_results": wiki_results.get("results", []) if wiki_results.get("success") else [],
                "data_sources": ["Star Citizen Tools Wiki"]
            }
            
            # Add summary
            if result["wiki_results"]:
                result["summary"] = f"Found {len(result['wiki_results'])} wiki articles about '{topic}'"
            else:
                result["summary"] = f"No specific wiki articles found for '{topic}'. Try more specific search terms."
            
            return result
            
        except Exception as e:
            return {
                "error": f"Failed to get information about '{topic}': {str(e)}",
                "success": False,
                "topic": topic
            }


# OpenWebUI Function Metadata
def get_tools():
    """Return the tools available in this module"""
    return Tools()


# Function definitions for OpenWebUI
FUNCTION_DEFINITIONS = [
    {
        "name": "search_star_citizen_wiki",
        "description": "Search the Star Citizen Tools wiki for game information, guides, tutorials, and documentation. Best for finding practical gameplay information, ship details, and how-to guides.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search term (e.g., 'mining', 'trading', 'ship components', 'missions', 'combat')"
                },
                "max_results": {
                    "type": "integer",
                    "description": "Maximum number of results to return",
                    "default": 10,
                    "minimum": 1,
                    "maximum": 25
                }
            },
            "required": ["query"]
        }
    },
    {
        "name": "get_wiki_page_content",
        "description": "Get detailed content from a specific Star Citizen Tools wiki page.",
        "parameters": {
            "type": "object",
            "properties": {
                "page_title": {
                    "type": "string",
                    "description": "The title of the wiki page (from search results or known page name)"
                },
                "include_full_content": {
                    "type": "boolean",
                    "description": "Whether to include the full page content (can be very long)",
                    "default": False
                }
            },
            "required": ["page_title"]
        }
    },
    {
        "name": "get_star_citizen_game_info",
        "description": "Get comprehensive information about a Star Citizen game topic by searching the wiki for practical gameplay information.",
        "parameters": {
            "type": "object",
            "properties": {
                "topic": {
                    "type": "string",
                    "description": "Game topic (e.g., 'mining', 'trading', 'combat', 'exploration', 'ship upgrades')"
                }
            },
            "required": ["topic"]
        }
    }
]


# Test function for development
if __name__ == "__main__":
    tools = Tools()
    
    # Test search
    print("Testing wiki search for 'mining':")
    result = tools.search_star_citizen_wiki("mining", max_results=5)
    print(json.dumps(result, indent=2))
