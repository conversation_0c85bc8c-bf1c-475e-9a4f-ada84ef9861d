#!/usr/bin/env python3
"""
Configuration Setup Script for Star Citizen Tools MCP Server
Helps users set up their API keys and configuration interactively.
"""

import os
import sys
import json
from config import get_config

def main():
    """Interactive configuration setup"""
    print("=" * 60)
    print("Star Citizen Tools MCP Server - Configuration Setup")
    print("=" * 60)
    print()
    
    config = get_config()
    
    # Show current status
    print("Current Configuration Status:")
    print("-" * 30)
    
    configured_services = config.list_configured_services()
    if configured_services:
        print("Configured API Keys:")
        for service in configured_services:
            print(f"  ✓ {service}")
    else:
        print("No API keys configured")
    
    print()
    print("Settings:")
    print(f"  Request Timeout: {config.get_setting('settings.request_timeout', 15)}s")
    print(f"  Rate Limit Delay: {config.get_setting('settings.rate_limit_delay', 1)}s")
    print(f"  Cache Enabled: {config.get_setting('settings.cache_enabled', True)}")
    print(f"  Debug Mode: {config.get_setting('settings.debug_mode', False)}")
    print()
    
    # Interactive setup
    while True:
        print("Configuration Options:")
        print("1. Set API Key")
        print("2. Remove API Key")
        print("3. Create .env file from template")
        print("4. Show current configuration")
        print("5. Test configuration")
        print("6. Exit")
        print()
        
        choice = input("Select an option (1-6): ").strip()
        
        if choice == "1":
            set_api_key_interactive(config)
        elif choice == "2":
            remove_api_key_interactive(config)
        elif choice == "3":
            create_env_file()
        elif choice == "4":
            show_configuration(config)
        elif choice == "5":
            test_configuration()
        elif choice == "6":
            print("Configuration setup complete!")
            break
        else:
            print("Invalid option. Please try again.")
        
        print()

def set_api_key_interactive(config):
    """Interactive API key setup"""
    print("\nAvailable services:")
    services = ["rsi", "wiki", "galactapedia", "citizens", "organizations"]
    for i, service in enumerate(services, 1):
        current_key = config.get_api_key(service)
        status = "✓ Configured" if current_key else "✗ Not configured"
        print(f"  {i}. {service} ({status})")
    
    print("  0. Cancel")
    
    try:
        choice = int(input("\nSelect service (0-5): ").strip())
        if choice == 0:
            return
        elif 1 <= choice <= len(services):
            service = services[choice - 1]
            
            current_key = config.get_api_key(service)
            if current_key:
                print(f"\nCurrent API key for {service}: {current_key[:8]}...")
                if input("Replace existing key? (y/N): ").lower() != 'y':
                    return
            
            api_key = input(f"\nEnter API key for {service}: ").strip()
            if api_key:
                config.set_api_key(service, api_key)
                print(f"✓ API key set for {service}")
            else:
                print("No API key entered.")
        else:
            print("Invalid selection.")
    except ValueError:
        print("Invalid input. Please enter a number.")

def remove_api_key_interactive(config):
    """Interactive API key removal"""
    configured_services = config.list_configured_services()
    
    if not configured_services:
        print("\nNo API keys are currently configured.")
        return
    
    print("\nConfigured services:")
    for i, service in enumerate(configured_services, 1):
        print(f"  {i}. {service}")
    
    print("  0. Cancel")
    
    try:
        choice = int(input("\nSelect service to remove (0-{}): ".format(len(configured_services))).strip())
        if choice == 0:
            return
        elif 1 <= choice <= len(configured_services):
            service = configured_services[choice - 1]
            if input(f"Remove API key for {service}? (y/N): ").lower() == 'y':
                config.remove_api_key(service)
                print(f"✓ API key removed for {service}")
        else:
            print("Invalid selection.")
    except ValueError:
        print("Invalid input. Please enter a number.")

def create_env_file():
    """Create .env file from template"""
    if os.path.exists('.env'):
        if input("\n.env file already exists. Overwrite? (y/N): ").lower() != 'y':
            return
    
    try:
        if os.path.exists('.env.example'):
            with open('.env.example', 'r') as src:
                content = src.read()
            
            with open('.env', 'w') as dst:
                dst.write(content)
            
            print("✓ .env file created from template")
            print("Edit the .env file to add your API keys and settings.")
        else:
            print("✗ .env.example template not found")
    except Exception as e:
        print(f"✗ Error creating .env file: {e}")

def show_configuration(config):
    """Show detailed configuration"""
    print("\nDetailed Configuration:")
    print("=" * 40)
    
    # API Keys
    print("\nAPI Keys:")
    services = ["rsi", "wiki", "galactapedia", "citizens", "organizations"]
    for service in services:
        api_key = config.get_api_key(service)
        if api_key:
            print(f"  {service}: {api_key[:8]}...{api_key[-4:]}")
        else:
            print(f"  {service}: Not configured")
    
    # Settings
    print("\nSettings:")
    settings = [
        ("Request Timeout", "settings.request_timeout", 15),
        ("User Agent", "settings.user_agent", "Default browser"),
        ("Rate Limit Delay", "settings.rate_limit_delay", 1),
        ("Cache Enabled", "settings.cache_enabled", True),
        ("Debug Mode", "settings.debug_mode", False)
    ]
    
    for name, key, default in settings:
        value = config.get_setting(key, default)
        print(f"  {name}: {value}")

def test_configuration():
    """Test the configuration by making a simple request"""
    print("\nTesting configuration...")
    
    try:
        # Test a simple wiki request
        from simple_example import search_wiki
        results = search_wiki("test", limit=1)
        
        if results:
            print("✓ Configuration test successful")
            print(f"  Found {len(results)} wiki results")
        else:
            print("⚠ Configuration test completed but no results found")
            print("  This might be normal depending on the search term")
    
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        print("  Check your network connection and API keys")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nSetup cancelled by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\nError: {e}")
        sys.exit(1)
