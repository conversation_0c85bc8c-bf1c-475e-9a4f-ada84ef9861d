"""
OpenWebUI Function: Star Citizen Player Profile Lookup
Allows users to look up Star Citizen player profiles by handle.
"""

import json
import sys
import os
from typing import Dict, Any, Optional

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from citizen_lookup import get_citizen_profile
    from config import get_config
    MODULES_AVAILABLE = True
except ImportError as e:
    MODULES_AVAILABLE = False
    import_error = str(e)


class Tools:
    def __init__(self):
        pass

    def lookup_star_citizen_player(
        self,
        handle: str,
        include_bio: bool = True,
        include_organization: bool = True
    ) -> Dict[str, Any]:
        """
        Look up a Star Citizen player profile by their handle.
        
        Args:
            handle (str): The player's handle/username in Star Citizen
            include_bio (bool): Whether to include the player's biography in the response
            include_organization (bool): Whether to include organization information
            
        Returns:
            Dict containing player profile information including name, rank, enlisted date, 
            location, main organization, and optionally biography.
        """
        
        if not MODULES_AVAILABLE:
            return {
                "error": f"Required modules not available: {import_error}",
                "success": False
            }
        
        if not handle or not handle.strip():
            return {
                "error": "Player handle is required",
                "success": False
            }
        
        handle = handle.strip()
        
        try:
            # Get the player profile using our existing function
            profile = get_citizen_profile(handle)
            
            if not profile:
                return {
                    "error": f"Player '{handle}' not found or profile is private",
                    "success": False,
                    "handle": handle
                }
            
            # Structure the response for the LLM
            result = {
                "success": True,
                "handle": profile.get("handle", handle),
                "name": profile.get("name", "Unknown"),
                "rank": profile.get("rank", "Unknown"),
                "enlisted": profile.get("enlisted", "Unknown"),
                "location": profile.get("location", "Unknown"),
                "fluency": profile.get("fluency", "Unknown")
            }
            
            # Add avatar if available
            if "avatar" in profile:
                result["avatar_url"] = profile["avatar"]
            
            # Add rank image if available
            if "rankImage" in profile:
                result["rank_image_url"] = profile["rankImage"]
            
            # Add organization information if requested and available
            if include_organization:
                org_info = {}
                if "mainOrg" in profile:
                    org_info["name"] = profile["mainOrg"]
                if "mainOrgSID" in profile:
                    org_info["sid"] = profile["mainOrgSID"]
                if "mainOrgRank" in profile:
                    org_info["rank"] = profile["mainOrgRank"]
                if "mainOrgLogo" in profile:
                    org_info["logo_url"] = profile["mainOrgLogo"]
                
                if org_info:
                    result["main_organization"] = org_info
            
            # Add biography if requested and available
            if include_bio and "bio" in profile and profile["bio"]:
                result["biography"] = profile["bio"]
            
            # Add metadata
            result["profile_url"] = f"https://robertsspaceindustries.com/en/citizens/{handle}"
            result["data_source"] = "Roberts Space Industries"
            
            return result
            
        except Exception as e:
            return {
                "error": f"Failed to lookup player '{handle}': {str(e)}",
                "success": False,
                "handle": handle
            }


# OpenWebUI Function Metadata
def get_tools():
    """Return the tools available in this module"""
    return Tools()


# Function definition for OpenWebUI
FUNCTION_DEFINITION = {
    "name": "lookup_star_citizen_player",
    "description": "Look up a Star Citizen player profile by their handle/username. Returns player information including name, rank, enlisted date, location, main organization, and biography.",
    "parameters": {
        "type": "object",
        "properties": {
            "handle": {
                "type": "string",
                "description": "The player's handle/username in Star Citizen (case-insensitive)"
            },
            "include_bio": {
                "type": "boolean",
                "description": "Whether to include the player's biography in the response",
                "default": True
            },
            "include_organization": {
                "type": "boolean", 
                "description": "Whether to include main organization information",
                "default": True
            }
        },
        "required": ["handle"]
    }
}


# Test function for development
if __name__ == "__main__":
    tools = Tools()
    
    # Test with a known player (you can change this)
    test_handle = "KenzoKai"
    print(f"Testing lookup for player: {test_handle}")
    
    result = tools.lookup_star_citizen_player(test_handle)
    print(json.dumps(result, indent=2))
