#!/usr/bin/env python3
"""
Test Script for OpenWebUI Star Citizen Functions
Verifies that all functions work correctly before deploying to OpenWebUI.
"""

import sys
import os
import json
from pathlib import Path

# Add the openwebui_functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'openwebui_functions'))

def test_function(module_name, function_name, test_args, description):
    """Test a specific function and return results"""
    print(f"\n{'='*60}")
    print(f"Testing: {description}")
    print(f"Function: {function_name}")
    print(f"Arguments: {test_args}")
    print('='*60)
    
    try:
        # Import the module
        module = __import__(module_name)
        tools = module.get_tools()
        
        # Get the function
        func = getattr(tools, function_name)
        
        # Call the function
        if isinstance(test_args, dict):
            result = func(**test_args)
        else:
            result = func(*test_args)
        
        # Display results
        print("✅ SUCCESS")
        print(json.dumps(result, indent=2))
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all function tests"""
    print("🚀 Star Citizen Tools - OpenWebUI Function Tests")
    print("=" * 60)
    
    # Test configuration
    print("\n📋 Checking Configuration...")
    try:
        from config import get_config
        config = get_config()
        configured_services = config.list_configured_services()
        print(f"✅ Configuration loaded")
        print(f"   Configured API keys: {configured_services}")
        
        if 'rsi' in configured_services or 'citizens' in configured_services:
            print("✅ RSI API key found")
        else:
            print("⚠️  No RSI API key found - some functions may not work optimally")
            
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False
    
    # Define tests
    tests = [
        {
            'module': 'citizen_lookup_function',
            'function': 'lookup_star_citizen_player',
            'args': {'handle': 'KenzoKai', 'include_bio': False},
            'description': 'Player Profile Lookup'
        },
        {
            'module': 'organization_lookup_function', 
            'function': 'lookup_star_citizen_organization',
            'args': {'sid': 'HMBCREW', 'include_members': False},
            'description': 'Organization Profile Lookup'
        },
        {
            'module': 'galactapedia_function',
            'function': 'search_star_citizen_lore',
            'args': {'query': 'Carrack', 'max_results': 3},
            'description': 'Galactapedia Search'
        },
        {
            'module': 'wiki_search_function',
            'function': 'search_star_citizen_wiki', 
            'args': {'query': 'mining', 'max_results': 3},
            'description': 'Wiki Search'
        }
    ]
    
    # Run tests
    passed = 0
    total = len(tests)
    
    for test in tests:
        success = test_function(
            test['module'],
            test['function'], 
            test['args'],
            test['description']
        )
        if success:
            passed += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"🎯 TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! Functions are ready for OpenWebUI.")
        print("\n📝 Next Steps:")
        print("1. Copy the functions to your OpenWebUI functions directory")
        print("2. Copy the required modules (config.py, citizen_lookup.py, etc.)")
        print("3. Restart OpenWebUI")
        print("4. Enable the functions in OpenWebUI settings")
        print("\nSee OPENWEBUI_INTEGRATION.md for detailed instructions.")
    else:
        print("⚠️  Some tests failed. Check the errors above and fix issues before deploying.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
