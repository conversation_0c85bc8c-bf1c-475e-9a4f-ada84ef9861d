#!/usr/bin/env python3
"""
Test script to demonstrate queries for New Babbage and 315p
"""

from masterSC import Tools
import json

def main():
    tools = Tools()
    
    print("🚀 TESTING: New Babbage and 315p Ship Queries")
    print("=" * 60)
    
    # Test 1: New Babbage
    print("\n🏙️ QUERY 1: New Babbage")
    print("-" * 40)
    result1 = tools.search_star_citizen_lore("New Babbage")
    
    if result1['success'] and result1['results']:
        for r in result1['results']:
            print(f"✅ FOUND: {r['title']}")
            print(f"   Type: {r['type']}")
            print(f"   Description: {r['description']}")
            print(f"   Source: {r['source']}")
            if 'tags' in r:
                print(f"   Tags: {', '.join(r['tags'])}")
    else:
        print("❌ No results found for New Babbage")
        if 'message' in result1:
            print(f"   Message: {result1['message']}")
    
    print("\n" + "=" * 60)
    
    # Test 2: 315p
    print("\n🚁 QUERY 2: 315p Ship")
    print("-" * 40)
    result2 = tools.search_star_citizen_lore("315p")
    
    if result2['success'] and result2['results']:
        for r in result2['results']:
            print(f"✅ FOUND: {r['title']}")
            print(f"   Type: {r['type']}")
            print(f"   Description: {r['description']}")
            print(f"   Source: {r['source']}")
            if 'tags' in r:
                print(f"   Tags: {', '.join(r['tags'])}")
    else:
        print("❌ No results found for 315p")
        if 'message' in result2:
            print(f"   Message: {result2['message']}")
    
    print("\n" + "=" * 60)
    
    # Test 3: Also test Mercury Star Runner to show it works
    print("\n🚀 QUERY 3: Mercury Star Runner (for comparison)")
    print("-" * 40)
    result3 = tools.search_star_citizen_lore("Mercury Star Runner")
    
    if result3['success'] and result3['results']:
        for r in result3['results']:
            print(f"✅ FOUND: {r['title']}")
            print(f"   Type: {r['type']}")
            print(f"   Description: {r['description'][:100]}...")
            print(f"   Source: {r['source']}")
    else:
        print("❌ No results found for Mercury Star Runner")
    
    print("\n🎉 All queries complete!")
    print("\nSUMMARY:")
    print(f"- New Babbage: {'✅ Found' if result1['success'] and result1['results'] else '❌ Not found'}")
    print(f"- 315p: {'✅ Found' if result2['success'] and result2['results'] else '❌ Not found'}")
    print(f"- Mercury Star Runner: {'✅ Found' if result3['success'] and result3['results'] else '❌ Not found'}")

if __name__ == "__main__":
    main()
