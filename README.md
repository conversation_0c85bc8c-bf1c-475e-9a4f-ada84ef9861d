# Star Citizen Tools MCP Server

This is a Model-Content-Provider (MCP) server for Star Citizen that references multiple data sources:

1. [Star Citizen Tools Wiki](https://starcitizen.tools/) - For game information and lore
2. [RSI Citizen Profiles](https://robertsspaceindustries.com/en/citizens/) - For player profiles
3. [RSI Organization Profiles](https://robertsspaceindustries.com/en/orgs/) - For organization details
4. [RSI Galactapedia](https://robertsspaceindustries.com/galactapedia) - For in-universe lore and information

## Project Structure

### Configuration Files
- `server.json` - Main server configuration file

### Modules
- `modules/wiki/module.json` - Wiki module for accessing Star Citizen Tools wiki
- `modules/citizens/module.json` - Citizens module for accessing RSI player profiles
- `modules/organizations/module.json` - Organizations module for accessing RSI organization profiles
- `modules/galactapedia/module.json` - Galactapedia module for accessing RSI Galactapedia articles and categories

### Client Scripts
- `client.py` - General Python client script to interact with the MCP server
- `simple_example.py` - Simple script to directly interact with the Star Citizen Tools wiki
- `citizen_lookup.py` - Script to retrieve citizen profiles from the RSI website
- `org_lookup.py` - Script to retrieve organization profiles and member lists from the RSI website
- `galactapedia_lookup.py` - Script to search and retrieve articles from the RSI Galactapedia

## Getting Started

### Prerequisites

- Python 3.6 or higher
- `requests` library (`pip install requests`)
- `beautifulsoup4` library (`pip install beautifulsoup4`) - For the standalone scripts

### API Key Configuration (Optional)

While most Star Citizen data sources work without API keys, having them can provide benefits like higher rate limits and access to additional features.

#### Setting Up API Keys

1. **Copy the environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file** and add your API keys:
   ```bash
   # RSI (Roberts Space Industries) API Key
   RSI_API_KEY=your_rsi_api_key_here

   # Other optional API keys
   WIKI_API_KEY=your_wiki_api_key_here
   GALACTAPEDIA_API_KEY=your_galactapedia_api_key_here
   ```

3. **Alternative: Use the client commands** to manage API keys:
   ```bash
   # Set an API key
   python client.py set-api-key rsi your_api_key_here

   # Check configuration status
   python client.py config-status

   # Remove an API key
   python client.py remove-api-key rsi
   ```

#### Configuration Options

You can customize various settings via environment variables or the configuration file:

- `REQUEST_TIMEOUT`: HTTP request timeout in seconds (default: 15)
- `RATE_LIMIT_DELAY`: Delay between requests to avoid rate limiting (default: 1)
- `CACHE_ENABLED`: Enable response caching (default: true)
- `DEBUG_MODE`: Enable debug logging (default: false)

#### Configuration Management Commands

The client includes several commands for managing configuration:

```bash
# Show current configuration status
python client.py config-status

# Set API keys
python client.py set-api-key <service> <api_key>

# Remove API keys
python client.py remove-api-key <service>

# List modules with API key status
python client.py list-modules
```

### Using the Client Script

The client script provides a simple way to interact with the MCP server. Here are some example commands:

#### List Available Modules

```bash
python client.py list-modules
```

#### List Resources in a Module

```bash
python client.py list-resources wiki
```

#### Call a Resource

To search for articles about "Anvil Carrack" using the wiki module:

```bash
python client.py call wiki search --params srsearch="Anvil Carrack"
```

To retrieve a specific wiki page about "Anvil Carrack":

```bash
python client.py call wiki wiki_page --params page="Anvil Carrack"
```

To retrieve a citizen profile by handle:

```bash
python client.py call citizens profile --params handle="KenzoKai"
```

To retrieve an organization profile by SID:

```bash
python client.py call organizations profile --params sid="HMBCREW"
```

To search the Galactapedia for articles about a specific topic:

```bash
python client.py call galactapedia search --params query="Carrack"
```

To retrieve a specific Galactapedia article by ID:

```bash
python client.py call galactapedia article --params articleId="R4ZGyLQaBl-carrack"
```

To browse articles in a specific Galactapedia category:

```bash
python client.py call galactapedia category --params categoryName="spacecraft"
```

### Using the Standalone Scripts

The project includes several standalone scripts that provide a more direct way to interact with the data sources.

#### Wiki Information

```bash
python simple_example.py search "Anvil Carrack"  # Search for articles
python simple_example.py page "Carrack"          # Get a specific page
```

#### Citizen Profiles

```bash
python citizen_lookup.py KenzoKai  # Look up a citizen profile
```

#### Organization Information

```bash
python org_lookup.py HMBCREW          # Look up an organization profile
python org_lookup.py HMBCREW members  # Look up an organization's members
```

#### Galactapedia Information

```bash
python galactapedia_lookup.py search Carrack       # Search for articles about Carrack
python galactapedia_lookup.py article R4ZGyLQaBl-carrack  # Get a specific article by ID
python galactapedia_lookup.py categories           # List all available categories
python galactapedia_lookup.py category spacecraft  # Browse articles in the spacecraft category
```

## Understanding MCP Servers

MCP (Model-Content-Provider) servers are a way to organize and access content from different sources through a standardized interface. In this project:

1. The `server.json` file defines the overall server configuration
2. Each module (wiki, citizens, organizations) provides access to a specific content source
3. Resources within modules define specific API endpoints and their parameters

This modular approach allows you to easily extend the server with additional content sources in the future.

## Data Output

All the scripts save their output to JSON files for easy integration with other applications:

- Wiki searches and pages: Results are displayed in the console
- Citizen profiles: Saved to `<handle>_profile.json` (e.g., `KenzoKai_profile.json`)
- Organization profiles: Saved to `<sid>_profile.json` (e.g., `HMBCREW_profile.json`)
- Organization members: Saved to `<sid>_members.json` (e.g., `HMBCREW_members.json`)
- Galactapedia searches: Saved to `galactapedia_search_<query>.json` (e.g., `galactapedia_search_Carrack.json`)
- Galactapedia articles: Saved to `galactapedia_article_<article_id>.json` (e.g., `galactapedia_article_R4ZGyLQaBl-carrack.json`)
- Galactapedia categories: Saved to `galactapedia_categories.json`
- Galactapedia category articles: Saved to `galactapedia_category_<category_name>.json` (e.g., `galactapedia_category_spacecraft.json`)

## Security and Best Practices

### API Key Security

- **Never commit API keys to version control**: The `.env` file and `config.json` are automatically ignored by git
- **Use environment variables in production**: Set API keys as environment variables rather than in files
- **Rotate API keys regularly**: Change your API keys periodically for better security
- **Use minimal permissions**: Only request the minimum API permissions needed for your use case

### Configuration Files

The project supports multiple configuration methods (in order of precedence):

1. **Environment variables** (highest priority)
2. **Configuration file** (`config.json`)
3. **Default values** (lowest priority)

### Rate Limiting

The system includes built-in rate limiting to prevent overwhelming the APIs:

- Default delay of 1 second between requests
- Configurable via `RATE_LIMIT_DELAY` environment variable
- Caching enabled by default to reduce API calls

### Error Handling

All scripts include comprehensive error handling:

- Network timeouts and connection errors
- Invalid API responses
- Missing or malformed data
- Rate limiting responses

## Troubleshooting

### Common Issues

1. **"Module not found" errors**: Ensure all required Python packages are installed
2. **Timeout errors**: Increase the `REQUEST_TIMEOUT` setting
3. **Rate limiting**: Increase the `RATE_LIMIT_DELAY` setting
4. **API key issues**: Use `python client.py config-status` to check your configuration

### Debug Mode

Enable debug mode for detailed logging:

```bash
# Via environment variable
export DEBUG_MODE=true

# Or check current status
python client.py config-status
```

### Getting Help

If you encounter issues:

1. Check the configuration status: `python client.py config-status`
2. Enable debug mode for more detailed output
3. Check the generated JSON files for data structure examples
4. Review the module configurations in the `modules/` directory
