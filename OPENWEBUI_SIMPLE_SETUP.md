# Star Citizen Tools - Simple OpenWebUI Setup

## 🎯 Single File Solution

Everything is now in **one file**: `masterSC.py`

This single script contains:
- ✅ Your RSI API key (`13e27ef0fc54d0ba93fa1bc820a79780`)
- ✅ All Star Citizen lookup functions
- ✅ Complete error handling
- ✅ No external dependencies (except standard Python libraries)

## 🚀 OpenWebUI Installation (Super Easy!)

### Step 1: Copy the Master Script

1. **Open OpenWebUI** in your browser
2. **Go to**: `Workspace` → `Functions` → `Add Function`
3. **Give it a name**: `Star Citizen Tools`
4. **Copy the entire content** of `masterSC.py`
5. **Paste it** into the function editor
6. **Save**

### Step 2: Install Dependencies

Make sure these Python packages are available to OpenWebUI:

```bash
pip install requests beautifulsoup4
```

### Step 3: Enable the Function

1. **Go to**: `Settings` → `Functions`
2. **Enable**: `Star Citizen Tools`
3. **Done!**

## 🎮 What You Can Ask Your AI

Once installed, you can ask questions like:

### Player Lookups
```
"Look up Star Citizen player <PERSON><PERSON><PERSON><PERSON>"
"What's the profile of citizen TestPilot?"
"Show me information about player handle 'SpaceExplorer'"
```

### Organization Information
```
"Tell me about the Star Citizen organization HMBCREW"
"What's the member count of organization TEST?"
"Show me the members of org CORP"
```

### Lore and Universe Information
```
"Tell me about the Carrack ship in Star Citizen"
"Search for information about ships in Star Citizen"
```

### Gameplay Information
```
"How does mining work in Star Citizen?"
"What are the best trading guides?"
"Tell me about combat mechanics"
```

## 🔧 Available Functions in masterSC.py

The single script provides these functions:

1. **`lookup_star_citizen_player(handle)`**
   - Get detailed player information
   - Returns: Name, rank, enlisted date, organization, biography

2. **`lookup_star_citizen_organization(sid)`**
   - Get organization details and member lists
   - Returns: Org info, member count, focus areas, member list

3. **`search_star_citizen_lore(query)`**
   - Search Star Citizen universe lore
   - Returns: Ships, planets, species, history

4. **`search_star_citizen_wiki(query)`**
   - Find gameplay guides and information
   - Returns: How-to guides, ship details, game mechanics

5. **`get_galactapedia_article(article_id)`**
   - Get specific lore articles
   - Returns: Detailed article content

6. **`get_wiki_page_content(page_title)`**
   - Get specific wiki pages
   - Returns: Full page content and information

## 🛠️ Troubleshooting

### Function Not Appearing
1. Make sure you copied the **entire** `masterSC.py` file
2. Check that the function is **enabled** in OpenWebUI settings
3. **Restart OpenWebUI** if needed

### Import Errors
1. Install missing packages: `pip install requests beautifulsoup4`
2. Make sure OpenWebUI can access the Python environment

### No Results
1. Check your internet connection
2. Verify the RSI website is accessible
3. Try different search terms

## 🔒 Security

- Your API key (`13e27ef0fc54d0ba93fa1bc820a79780`) is embedded in the script
- Only makes read-only requests to public APIs
- No sensitive data is stored beyond the API key

## ✅ Test Before Using

You can test the script locally first:

```bash
python masterSC.py
```

This will run a test lookup to verify everything works.

## 🎉 That's It!

Your AI assistant now has comprehensive access to Star Citizen information with just **one file**!

The AI can help answer questions about:
- **Players and their profiles**
- **Organizations and members**
- **Ships, planets, and lore**
- **Gameplay guides and tutorials**

All from official Star Citizen sources! 🚀
