#!/usr/bin/env python3
"""
Test script to search for Banu lore
"""

from masterSC import Tools
import json

def main():
    tools = Tools()
    
    print("🔍 SEARCHING FOR: Banu")
    print("=" * 50)
    
    result = tools.search_star_citizen_lore("Banu", max_results=10)
    
    if result['success'] and result['results']:
        print(f"✅ Found {len(result['results'])} results about the Banu:")
        print()
        
        for i, r in enumerate(result['results'], 1):
            print(f"{i}. {r['title']} ({r['source']})")
            print(f"   Type: {r['type']}")
            print(f"   Description: {r['description']}")
            if 'tags' in r:
                print(f"   Tags: {', '.join(r['tags'])}")
            print()
    else:
        print("❌ No results found for Banu")
        if 'message' in result:
            print(f"Message: {result['message']}")
    
    print("=" * 50)
    print("🎯 SUMMARY:")
    print(f"Total results: {result.get('total_found', 0)}")
    if 'sources' in result:
        print(f"Galactapedia results: {result['sources'].get('galactapedia_results', 0)}")
        print(f"Wiki results: {result['sources'].get('wiki_results', 0)}")

if __name__ == "__main__":
    main()
