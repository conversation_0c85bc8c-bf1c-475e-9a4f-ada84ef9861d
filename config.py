"""
Configuration management for Star Citizen Tools MCP Server
Handles API keys and other configuration settings from environment variables and config files.
"""

import os
import json
from typing import Dict, Optional, Any
from pathlib import Path


class Config:
    """Configuration manager for API keys and settings"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration manager
        
        Args:
            config_file: Optional path to JSON configuration file
        """
        self.config_file = config_file or "config.json"
        self._config_data = {}
        self._load_config()
    
    def _load_config(self):
        """Load configuration from file and environment variables"""
        # Load from config file if it exists
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    self._config_data = json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Warning: Could not load config file {self.config_file}: {e}")
                self._config_data = {}
        
        # Override with environment variables
        self._load_env_variables()
    
    def _load_env_variables(self):
        """Load configuration from environment variables"""
        # API Keys
        env_mappings = {
            'RSI_API_KEY': ['api_keys', 'rsi'],
            'WIKI_API_KEY': ['api_keys', 'wiki'],
            'GALACTAPEDIA_API_KEY': ['api_keys', 'galactapedia'],
            'CITIZENS_API_KEY': ['api_keys', 'citizens'],
            'ORGANIZATIONS_API_KEY': ['api_keys', 'organizations'],
            
            # General settings
            'REQUEST_TIMEOUT': ['settings', 'request_timeout'],
            'USER_AGENT': ['settings', 'user_agent'],
            'RATE_LIMIT_DELAY': ['settings', 'rate_limit_delay'],
            'CACHE_ENABLED': ['settings', 'cache_enabled'],
            'DEBUG_MODE': ['settings', 'debug_mode'],
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert boolean strings
                if value.lower() in ('true', 'false'):
                    value = value.lower() == 'true'
                # Convert numeric strings
                elif value.isdigit():
                    value = int(value)
                elif value.replace('.', '').isdigit():
                    value = float(value)
                
                # Set nested configuration
                self._set_nested_config(config_path, value)
    
    def _set_nested_config(self, path: list, value: Any):
        """Set a nested configuration value"""
        current = self._config_data
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[path[-1]] = value
    
    def get_api_key(self, service: str) -> Optional[str]:
        """
        Get API key for a specific service
        
        Args:
            service: Service name (e.g., 'rsi', 'wiki', 'galactapedia')
            
        Returns:
            API key string or None if not found
        """
        return self._config_data.get('api_keys', {}).get(service)
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration setting
        
        Args:
            key: Setting key (supports dot notation like 'settings.timeout')
            default: Default value if setting not found
            
        Returns:
            Setting value or default
        """
        keys = key.split('.')
        current = self._config_data
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current
    
    def get_headers(self, service: str = None) -> Dict[str, str]:
        """
        Get HTTP headers for requests, including API key if available
        
        Args:
            service: Service name for API key lookup
            
        Returns:
            Dictionary of HTTP headers
        """
        headers = {
            "User-Agent": self.get_setting('settings.user_agent', 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"),
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        # Add API key if available
        if service:
            api_key = self.get_api_key(service)
            if api_key:
                # Different services may use different header names
                if service == 'rsi':
                    headers['Authorization'] = f'Bearer {api_key}'
                elif service in ['wiki', 'galactapedia']:
                    headers['X-API-Key'] = api_key
                else:
                    headers['Authorization'] = f'Bearer {api_key}'
        
        return headers
    
    def get_request_config(self, service: str = None) -> Dict[str, Any]:
        """
        Get request configuration including timeout and other settings
        
        Args:
            service: Service name for specific configurations
            
        Returns:
            Dictionary of request configuration
        """
        return {
            'timeout': self.get_setting('settings.request_timeout', 15),
            'headers': self.get_headers(service),
            'verify': True  # Always verify SSL certificates
        }
    
    def save_config(self):
        """Save current configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self._config_data, f, indent=2)
        except IOError as e:
            print(f"Error saving config file: {e}")
    
    def set_api_key(self, service: str, api_key: str):
        """
        Set API key for a service and save to config file
        
        Args:
            service: Service name
            api_key: API key string
        """
        if 'api_keys' not in self._config_data:
            self._config_data['api_keys'] = {}
        
        self._config_data['api_keys'][service] = api_key
        self.save_config()
    
    def remove_api_key(self, service: str):
        """
        Remove API key for a service
        
        Args:
            service: Service name
        """
        if 'api_keys' in self._config_data and service in self._config_data['api_keys']:
            del self._config_data['api_keys'][service]
            self.save_config()
    
    def list_configured_services(self) -> list:
        """Get list of services with configured API keys"""
        return list(self._config_data.get('api_keys', {}).keys())
    
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled"""
        return self.get_setting('settings.debug_mode', False)


# Global configuration instance
_config_instance = None

def get_config() -> Config:
    """Get global configuration instance"""
    global _config_instance
    if _config_instance is None:
        _config_instance = Config()
    return _config_instance

def reload_config():
    """Reload configuration from files and environment"""
    global _config_instance
    _config_instance = Config()
