"""
OpenWebUI Function: Star Citizen Galactapedia Search
Allows users to search the Star Citizen Galactapedia for lore, ships, and universe information.
"""

import json
import sys
import os
from typing import Dict, Any, Optional, List

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from galactapedia_lookup import GalactapediaClient
    from config import get_config
    MODULES_AVAILABLE = True
except ImportError as e:
    MODULES_AVAILABLE = False
    import_error = str(e)


class Tools:
    def __init__(self):
        if MODULES_AVAILABLE:
            self.client = GalactapediaClient()
        else:
            self.client = None

    def search_star_citizen_lore(
        self,
        query: str,
        max_results: int = 10
    ) -> Dict[str, Any]:
        """
        Search the Star Citizen Galactapedia for lore, ships, locations, and universe information.
        
        Args:
            query (str): Search term (e.g., "Carrack", "Banu", "Terra", "UEE")
            max_results (int): Maximum number of results to return
            
        Returns:
            Dict containing search results with titles, descriptions, and article IDs.
        """
        
        if not MODULES_AVAILABLE:
            return {
                "error": f"Required modules not available: {import_error}",
                "success": False
            }
        
        if not query or not query.strip():
            return {
                "error": "Search query is required",
                "success": False
            }
        
        query = query.strip()
        
        try:
            # Search the Galactapedia
            search_results = self.client.search_articles(query)
            
            if not search_results:
                return {
                    "success": True,
                    "query": query,
                    "results": [],
                    "message": f"No results found for '{query}'. Try different search terms or check spelling."
                }
            
            # Limit results and format for LLM consumption
            limited_results = search_results[:max_results] if len(search_results) > max_results else search_results
            
            formatted_results = []
            for result in limited_results:
                formatted_result = {
                    "title": result.get("title", "Unknown"),
                    "description": result.get("description", "No description available"),
                    "article_id": result.get("id", ""),
                    "type": result.get("type", "Unknown"),
                    "url": result.get("url", "")
                }
                
                # Add tags if available
                if "tags" in result and result["tags"]:
                    formatted_result["tags"] = result["tags"]
                
                formatted_results.append(formatted_result)
            
            return {
                "success": True,
                "query": query,
                "results": formatted_results,
                "total_found": len(search_results),
                "showing": len(formatted_results),
                "data_source": "Star Citizen Galactapedia"
            }
            
        except Exception as e:
            return {
                "error": f"Failed to search Galactapedia for '{query}': {str(e)}",
                "success": False,
                "query": query
            }

    def get_galactapedia_article(
        self,
        article_id: str
    ) -> Dict[str, Any]:
        """
        Get detailed information about a specific Galactapedia article.
        
        Args:
            article_id (str): The article ID (e.g., "R4ZGyLQaBl-carrack")
            
        Returns:
            Dict containing the full article content, metadata, and related information.
        """
        
        if not MODULES_AVAILABLE:
            return {
                "error": f"Required modules not available: {import_error}",
                "success": False
            }
        
        if not article_id or not article_id.strip():
            return {
                "error": "Article ID is required",
                "success": False
            }
        
        article_id = article_id.strip()
        
        try:
            # Get the article
            article = self.client.get_article(article_id)
            
            if not article:
                return {
                    "error": f"Article '{article_id}' not found",
                    "success": False,
                    "article_id": article_id
                }
            
            result = {
                "success": True,
                "article_id": article.get("id", article_id),
                "title": article.get("title", "Unknown"),
                "content": article.get("content", "No content available"),
                "url": article.get("url", "")
            }
            
            # Add metadata if available
            if "metadata" in article and article["metadata"]:
                result["metadata"] = article["metadata"]
            
            # Add tags if available
            if "tags" in article and article["tags"]:
                result["tags"] = article["tags"]
            
            result["data_source"] = "Star Citizen Galactapedia"
            
            return result
            
        except Exception as e:
            return {
                "error": f"Failed to get article '{article_id}': {str(e)}",
                "success": False,
                "article_id": article_id
            }

    def browse_galactapedia_category(
        self,
        category: str,
        max_results: int = 20
    ) -> Dict[str, Any]:
        """
        Browse articles in a specific Galactapedia category.
        
        Args:
            category (str): Category name (e.g., "spacecraft", "planets", "species", "history")
            max_results (int): Maximum number of articles to return
            
        Returns:
            Dict containing articles in the specified category.
        """
        
        if not MODULES_AVAILABLE:
            return {
                "error": f"Required modules not available: {import_error}",
                "success": False
            }
        
        if not category or not category.strip():
            return {
                "error": "Category name is required",
                "success": False
            }
        
        category = category.strip().lower()
        
        try:
            # Get category articles
            articles = self.client.get_category(category)
            
            if not articles:
                return {
                    "success": True,
                    "category": category,
                    "articles": [],
                    "message": f"No articles found in category '{category}' or category doesn't exist."
                }
            
            # Limit results and format for LLM consumption
            limited_articles = articles[:max_results] if len(articles) > max_results else articles
            
            formatted_articles = []
            for article in limited_articles:
                formatted_article = {
                    "title": article.get("title", "Unknown"),
                    "description": article.get("description", "No description available"),
                    "article_id": article.get("id", ""),
                    "url": article.get("url", "")
                }
                
                # Add tags if available
                if "tags" in article and article["tags"]:
                    formatted_article["tags"] = article["tags"]
                
                formatted_articles.append(formatted_article)
            
            return {
                "success": True,
                "category": category,
                "articles": formatted_articles,
                "total_found": len(articles),
                "showing": len(formatted_articles),
                "data_source": "Star Citizen Galactapedia"
            }
            
        except Exception as e:
            return {
                "error": f"Failed to browse category '{category}': {str(e)}",
                "success": False,
                "category": category
            }


# OpenWebUI Function Metadata
def get_tools():
    """Return the tools available in this module"""
    return Tools()


# Function definitions for OpenWebUI
FUNCTION_DEFINITIONS = [
    {
        "name": "search_star_citizen_lore",
        "description": "Search the Star Citizen Galactapedia for lore, ships, locations, species, and universe information. Great for finding information about Star Citizen's universe, ships, planets, organizations, and history.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search term (e.g., 'Carrack', 'Banu', 'Terra', 'UEE', 'Aegis Dynamics')"
                },
                "max_results": {
                    "type": "integer",
                    "description": "Maximum number of results to return",
                    "default": 10,
                    "minimum": 1,
                    "maximum": 50
                }
            },
            "required": ["query"]
        }
    },
    {
        "name": "get_galactapedia_article",
        "description": "Get detailed information about a specific Galactapedia article using its article ID.",
        "parameters": {
            "type": "object",
            "properties": {
                "article_id": {
                    "type": "string",
                    "description": "The article ID from a search result (e.g., 'R4ZGyLQaBl-carrack')"
                }
            },
            "required": ["article_id"]
        }
    },
    {
        "name": "browse_galactapedia_category",
        "description": "Browse articles in a specific Galactapedia category to discover related content.",
        "parameters": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "string",
                    "description": "Category name",
                    "enum": ["spacecraft", "planets", "species", "history", "military", "locations", "organizations", "technology", "people"]
                },
                "max_results": {
                    "type": "integer",
                    "description": "Maximum number of articles to return",
                    "default": 20,
                    "minimum": 1,
                    "maximum": 100
                }
            },
            "required": ["category"]
        }
    }
]


# Test function for development
if __name__ == "__main__":
    tools = Tools()
    
    # Test search
    print("Testing Galactapedia search for 'Carrack':")
    result = tools.search_star_citizen_lore("Carrack", max_results=5)
    print(json.dumps(result, indent=2))
