{"name": "StarCitizenTools", "description": "Star Citizen information server with data from the Star Citizen Tools wiki and RSI website", "version": "1.0.0", "configuration": {"api_keys": {"description": "API keys for various services. Can be set via environment variables or config.json", "services": {"rsi": {"description": "Roberts Space Industries API key for citizen profiles, organizations, and Galactapedia", "env_var": "RSI_API_KEY", "required": false}, "wiki": {"description": "Star Citizen Tools wiki API key (optional, for higher rate limits)", "env_var": "WIKI_API_KEY", "required": false}, "galactapedia": {"description": "Galactapedia-specific API key (if different from RSI)", "env_var": "GALACTAPEDIA_API_KEY", "required": false}, "citizens": {"description": "Citizens-specific API key (if different from RSI)", "env_var": "CITIZENS_API_KEY", "required": false}, "organizations": {"description": "Organizations-specific API key (if different from RSI)", "env_var": "ORGANIZATIONS_API_KEY", "required": false}}}, "settings": {"description": "General configuration settings", "options": {"request_timeout": {"description": "HTTP request timeout in seconds", "env_var": "REQUEST_TIMEOUT", "default": 15, "type": "integer"}, "user_agent": {"description": "Custom User-Agent string for HTTP requests", "env_var": "USER_AGENT", "default": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "type": "string"}, "rate_limit_delay": {"description": "Delay between requests in seconds to avoid rate limiting", "env_var": "RATE_LIMIT_DELAY", "default": 1, "type": "number"}, "cache_enabled": {"description": "Enable response caching for better performance", "env_var": "CACHE_ENABLED", "default": true, "type": "boolean"}, "debug_mode": {"description": "Enable debug logging and additional information", "env_var": "DEBUG_MODE", "default": false, "type": "boolean"}}}}, "modules": [{"name": "wiki", "source": "./modules/wiki/module.json", "api_key_service": "wiki"}, {"name": "citizens", "source": "./modules/citizens/module.json", "api_key_service": "citizens"}, {"name": "organizations", "source": "./modules/organizations/module.json", "api_key_service": "organizations"}, {"name": "galactapedia", "source": "./modules/galactapedia/module.json", "api_key_service": "galactapedia"}]}